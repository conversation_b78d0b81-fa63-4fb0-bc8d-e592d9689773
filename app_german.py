from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file, Response
import os
import sys
import json
import urllib.parse
from werkzeug.utils import secure_filename
from hr_database_working import HRDatabase
from matcher import CVMatcher
from cv_extractor import CVDataExtractor
try:
    from listmonk_integration import ListmonkEmailService
    EMAIL_SERVICE_TYPE = "listmonk"
except ImportError:
    EMAIL_SERVICE_TYPE = "basic"

from email_service import EmailService
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()
from datetime import datetime, date
import glob
from functools import wraps
# Lazy imports for faster startup
def get_openpyxl():
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    return openpyxl, Font, PatternFill, Alignment

from io import BytesIO

# Simple document processing functions
def extract_text_from_pdf(filepath):
    """Extract text from PDF using PyMuPDF"""
    try:
        import fitz
        doc = fitz.open(filepath)
        text = ""
        for page in doc:
            text += page.get_text()
        doc.close()
        return text
    except Exception as e:
        print(f"Error extracting PDF text: {e}")
        return ""

def extract_text_from_docx(filepath):
    """Extract text from DOCX using python-docx"""
    try:
        from docx import Document
        doc = Document(filepath)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    except Exception as e:
        print(f"Error extracting DOCX text: {e}")
        return ""

app = Flask(__name__)
app.secret_key = 'hr_management_secret_key_change_in_production'

# Use absolute path for upload folder to ensure consistency
script_dir = os.path.dirname(os.path.abspath(__file__))
app.config['UPLOAD_FOLDER'] = os.path.join(script_dir, 'uploads')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size
app.config['ALLOWED_EXTENSIONS'] = {'pdf', 'docx'}

# Add custom filter for newlines to <br> conversion
@app.template_filter('nl2br')
def nl2br_filter(text):
    """Convert newlines to <br> tags"""
    if text:
        return text.replace('\n', '<br>')
    return text

# Create uploads folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize database, matcher, and email service
hr_db = HRDatabase()
cv_matcher = CVMatcher()

# Initialize email service (Listmonk or fallback)
if EMAIL_SERVICE_TYPE == "listmonk":
    try:
        # Initialize with environment variables for security
        email_service = ListmonkEmailService(
            base_url=os.environ.get('LISTMONK_URL'),
            username=os.environ.get('LISTMONK_USERNAME'),
            password=os.environ.get('LISTMONK_PASSWORD')
        )
        print("✅ Using Listmonk professional email service")
    except Exception as e:
        print(f"⚠️ Listmonk not available, falling back to basic email service: {e}")
        email_service = EmailService(
            mail_server=os.environ.get('SMTP_HOST'),
            mail_port=int(os.environ.get('SMTP_PORT', 587)),
            mail_username=os.environ.get('SMTP_USERNAME'),
            mail_password=os.environ.get('SMTP_PASSWORD'),
            default_sender=os.environ.get('DEFAULT_SENDER_EMAIL')
        )
        EMAIL_SERVICE_TYPE = "basic"
else:
    email_service = EmailService(
        mail_server=os.environ.get('SMTP_HOST'),
        mail_port=int(os.environ.get('SMTP_PORT', 587)),
        mail_username=os.environ.get('SMTP_USERNAME'),
        mail_password=os.environ.get('SMTP_PASSWORD'),
        default_sender=os.environ.get('DEFAULT_SENDER_EMAIL')
    )
    print("📧 Using basic email service")

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# Context processor for templates
@app.context_processor
def inject_now():
    return {'now': datetime.now()}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def process_document(filepath):
    """Process document and extract text"""
    filename = os.path.basename(filepath)
    file_ext = os.path.splitext(filename)[1].lower()

    if file_ext == '.pdf':
        content = extract_text_from_pdf(filepath)
    elif file_ext == '.docx':
        content = extract_text_from_docx(filepath)
    else:
        return None

    return content

# Authentication routes
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        if not username or not password:
            flash('Please provide both username and password', 'error')
            return render_template('login.html')

        user = hr_db.authenticate_user(username, password)
        if user:
            session['user_id'] = user.id
            session['username'] = user.username
            flash(f'Welcome back, {user.username}!', 'success')
            return redirect(url_for('home'))
        else:
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully', 'info')
    return redirect(url_for('login'))

@app.route('/')
@login_required
def home():
    """Home page / Dashboard"""
    jobs_count = len(hr_db.get_all_jobs())

    # Get list of logo images from static/img folder
    logo_images = []
    static_img_dir = os.path.join(os.path.dirname(__file__), 'static', 'img')
    if os.path.exists(static_img_dir):
        for ext in ['png', 'jpg', 'jpeg', 'gif']:
            logo_images.extend([os.path.basename(f) for f in glob.glob(os.path.join(static_img_dir, f'*.{ext}'))])

    # Limit to 3 images if more are found
    logo_images = logo_images[:3]

    return render_template('index.html', jobs_count=jobs_count, logo_images=logo_images)

@app.route('/jobs')
@login_required
def jobs():
    """List all jobs"""
    all_jobs = hr_db.get_all_jobs()

    # Create a list of dictionaries with job info and CV count
    jobs_with_counts = []
    for job in all_jobs:
        cv_count = len(hr_db.get_cvs_for_job(job.title))

        # Get main responsible person safely
        main_responsible = "Not Assigned"
        responsible_people = hr_db.get_responsible_people_for_job(job.id)
        for person in responsible_people:
            if person.is_main_responsible:
                main_responsible = person.name
                break

        # Calculate days remaining safely
        days_remaining = None
        if job.end_date:
            today = date.today()
            days_remaining = (job.end_date - today).days

        jobs_with_counts.append({
            'id': job.id,
            'title': job.title,
            'description': job.description,
            'platform': job.platform,
            'job_url': job.job_url,
            'start_date': job.start_date,
            'end_date': job.end_date,
            'status': job.status,
            'days_remaining': days_remaining,
            'main_responsible_person': main_responsible,
            'cv_count': cv_count
        })

    return render_template('jobs.html', jobs=jobs_with_counts)

@app.route('/jobs/add', methods=['GET', 'POST'])
@login_required
def add_job():
    """Add a new job (supports PDF upload for description)"""
    if request.method == 'POST':
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()
        platform = request.form.get('platform', '').strip()
        job_url = request.form.get('job_url', '').strip()
        status = request.form.get('status', 'Active').strip()
        start_date_str = request.form.get('start_date', '').strip()
        end_date_str = request.form.get('end_date', '').strip()
        main_name = request.form.get('main_name', '').strip()
        main_email = request.form.get('main_email', '').strip()

        # Handle uploaded PDF for description
        if 'job_pdf' in request.files:
            job_pdf = request.files['job_pdf']
            if job_pdf and job_pdf.filename and job_pdf.filename.lower().endswith('.pdf'):
                try:
                    import fitz  # PyMuPDF
                    pdf_bytes = job_pdf.read()
                    doc = fitz.open(stream=pdf_bytes, filetype="pdf")
                    extracted_text = ""
                    for page in doc:
                        extracted_text += page.get_text()
                    doc.close()
                    if not description.strip() and extracted_text.strip():
                        description = extracted_text.strip()
                        flash('Job description extracted from PDF successfully!', 'info')
                except Exception as e:
                    flash(f"Error reading PDF: {e}", 'error')
                    return render_template('add_job.html')

        if not title or not description:
            flash('Please provide both a title and description (text or PDF)', 'error')
            return render_template('add_job.html')

        if not main_name or not main_email:
            flash('Please provide main responsible person details', 'error')
            return render_template('add_job.html', title=title, description=description)

        # Parse dates
        start_date = None
        end_date = None
        try:
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            flash('Invalid date format', 'error')
            return render_template('add_job.html', title=title, description=description)

        try:
            # Prepare responsible people data
            responsible_people = [
                {
                    'name': main_name,
                    'email': main_email,
                    'is_main': True
                }
            ]

            # Add additional responsible people
            additional_names = request.form.getlist('additional_names[]')
            additional_emails = request.form.getlist('additional_emails[]')

            for name, email in zip(additional_names, additional_emails):
                if name.strip() and email.strip():
                    responsible_people.append({
                        'name': name.strip(),
                        'email': email.strip(),
                        'is_main': False
                    })

            hr_db.add_job(
                title=title,
                description=description,
                responsible_people=responsible_people,
                platform=platform if platform else None,
                job_url=job_url if job_url else None,
                start_date=start_date,
                end_date=end_date,
                status=status
            )
            flash(f'Job "{title}" added successfully with {len(responsible_people)} responsible people!', 'success')
            return redirect(url_for('jobs'))
        except ValueError as e:
            flash(str(e), 'error')
            return render_template('add_job.html', title=title, description=description)

    return render_template('add_job.html')

@app.route('/jobs/<path:job_title>')
@login_required
def job_detail(job_title):
    """Display detailed job information"""
    try:
        # Decode URL-encoded job title
        job_title = urllib.parse.unquote(job_title)

        # Get job and CVs in a single session to avoid lazy loading issues
        job_data, cv_data = hr_db.get_job_with_cvs(job_title)
        if not job_data:
            flash(f'Job "{job_title}" not found', 'error')
            return redirect(url_for('jobs'))

        # Get responsible people
        responsible_people = hr_db.get_responsible_people_for_job(job_data['id'])

        # Calculate days remaining
        days_remaining = None
        if job_data['end_date']:
            today = date.today()
            days_remaining = (job_data['end_date'] - today).days

        # Add additional data to job_data
        job_data.update({
            'days_remaining': days_remaining,
            'cv_count': len(cv_data),
            'responsible_people': responsible_people,
            'cvs': cv_data  # Add CVs for email functionality
        })

        return render_template('job_detail.html', job=job_data)

    except Exception as e:
        flash(f'Error loading job details: {str(e)}', 'error')
        return redirect(url_for('jobs'))

# Email functionality routes
@app.route('/jobs/<path:job_title>/email', methods=['GET', 'POST'])
@login_required
def job_email(job_title):
    """Send emails to job applicants"""
    try:
        # Decode URL-encoded job title
        job_title = urllib.parse.unquote(job_title)

        # Get job and CVs in a single session to avoid lazy loading issues
        job_data, cv_data = hr_db.get_job_with_cvs(job_title)
        if not job_data:
            flash(f'Job "{job_title}" not found', 'error')
            return redirect(url_for('jobs'))

        # Convert data to objects for template compatibility
        class SimpleJob:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)

        class SimpleCV:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)

        job = SimpleJob(job_data)
        job_cvs = [SimpleCV(cv) for cv in cv_data]

        # Add CVs to job object for template compatibility
        job.cvs = job_cvs

        if request.method == 'POST':
            # Get form data
            email_type = request.form.get('email_type')
            subject = request.form.get('subject', '').strip()
            custom_message = request.form.get('custom_message', '').strip()
            selected_cvs = request.form.getlist('selected_cvs')

            if not subject:
                flash('Please provide an email subject', 'error')
                return render_template('job_email.html', job=job)

            # Get email template
            templates = email_service.get_default_templates('de')  # German templates

            if email_type == 'custom' and custom_message:
                template = custom_message
            elif email_type in templates:
                template = templates[email_type]
            else:
                flash('Please select a valid email template or provide custom message', 'error')
                return render_template('job_email.html', job=job)

            # Prepare recipients
            recipients = []
            cvs_to_email = job_cvs if not selected_cvs else [cv for cv in job_cvs if str(cv.id) in selected_cvs]

            for cv in cvs_to_email:
                # Try to extract email from CV content or use a placeholder
                email = extract_email_from_cv_content(cv.content)
                if email:
                    recipients.append({
                        'email': email,
                        'name': getattr(cv, 'candidate_name', 'Candidate'),
                        'job_title': job.title,
                        'status': 'under review',
                        'additional_info': 'We will contact you soon with updates.'
                    })

            if not recipients:
                flash('No valid email addresses found in selected CVs', 'error')
                return render_template('job_email.html', job=job)

            # Send emails
            try:
                result = email_service.send_bulk_emails(
                    recipients=recipients,
                    subject=subject,
                    template=template,
                    async_send=False
                )

                flash(f'Emails sent successfully! Sent: {result["success"]}, Failed: {result["failed"]}', 'success')
                return redirect(url_for('job_detail', job_title=job_title))

            except Exception as e:
                flash(f'Error sending emails: {str(e)}', 'error')
                return render_template('job_email.html', job=job)

        # GET request - show email form
        return render_template('job_email.html', job=job)

    except Exception as e:
        flash(f'Error accessing email system: {str(e)}', 'error')
        return redirect(url_for('jobs'))

def extract_email_from_cv_content(content):
    """Extract email address from CV content using multiple robust patterns"""
    import re
    if not content:
        return None

    # Clean the content - remove extra whitespace and normalize
    content = re.sub(r'\s+', ' ', content.strip())

    # Multiple email patterns to catch different formats found in CVs
    email_patterns = [
        # Standard email patterns
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
        r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}',

        # Email with spaces (common in PDFs)
        r'[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Z|a-z]{2,}',

        # Email with line breaks or special characters
        r'[A-Za-z0-9._%+-]+[\s\n]*@[\s\n]*[A-Za-z0-9.-]+[\s\n]*\.[\s\n]*[A-Z|a-z]{2,}',

        # Very permissive pattern for malformed emails
        r'[A-Za-z0-9][A-Za-z0-9._%+-]*@[A-Za-z0-9][A-Za-z0-9.-]*\.[A-Za-z]{2,}',

        # Pattern for emails with dots in unusual places
        r'[A-Za-z0-9]+[._]?[A-Za-z0-9]*@[A-Za-z0-9]+[.-]?[A-Za-z0-9]*\.[A-Za-z]{2,}',
    ]

    for pattern in email_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
        if matches:
            # Clean up the found email (remove spaces, newlines)
            email = re.sub(r'\s+', '', matches[0])
            # Validate it looks like a real email
            if '@' in email and '.' in email.split('@')[1]:
                return email.lower()

    # Last resort: look for any text with @ and try to extract email-like patterns
    at_matches = re.findall(r'[^\s]{1,50}@[^\s]{1,50}', content)
    for match in at_matches:
        # Clean and validate
        cleaned = re.sub(r'[^\w@.-]', '', match)
        if re.match(r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$', cleaned):
            return cleaned.lower()

    return None

# Debug route to check CV content and email extraction
@app.route('/debug/cvs/<path:job_title>')
@login_required
def debug_cvs(job_title):
    """Debug route to check CV content and email extraction"""
    try:
        job_title = urllib.parse.unquote(job_title)
        job_data, cv_data = hr_db.get_job_with_cvs(job_title)

        if not job_data:
            return f"Job '{job_title}' not found"

        debug_info = []
        debug_info.append(f"<h2>Debug Info for Job: {job_title}</h2>")
        debug_info.append(f"<p>Total CVs found: {len(cv_data)}</p>")

        for i, cv in enumerate(cv_data, 1):
            debug_info.append(f"<h3>CV #{i}: {cv.get('filename', 'Unknown')}</h3>")
            debug_info.append(f"<p><strong>Candidate Name:</strong> {cv.get('candidate_name', 'Not set')}</p>")

            content = cv.get('content', '')
            debug_info.append(f"<p><strong>Content Length:</strong> {len(content)} characters</p>")

            # Show first 500 characters of content
            preview = content[:500] + "..." if len(content) > 500 else content
            debug_info.append(f"<p><strong>Content Preview:</strong></p>")
            debug_info.append(f"<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>{preview}</pre>")

            # Test email extraction with detailed analysis
            extracted_email = extract_email_from_cv_content(content)
            if extracted_email:
                debug_info.append(f"<p><strong>✅ Email Found:</strong> {extracted_email}</p>")
            else:
                debug_info.append(f"<p><strong>❌ No Email Found</strong></p>")

                # Try to find any @ symbols
                import re
                at_positions = [i for i, char in enumerate(content) if char == '@']
                if at_positions:
                    debug_info.append(f"<p><strong>@ symbols found at positions:</strong> {at_positions[:5]}</p>")
                    for pos in at_positions[:3]:  # Show context around first 3 @ symbols
                        start = max(0, pos - 30)
                        end = min(len(content), pos + 30)
                        context = content[start:end]
                        debug_info.append(f"<p><strong>Context around @:</strong> <code>{context.replace('<', '&lt;').replace('>', '&gt;')}</code></p>")

                    # Try different patterns manually
                    debug_info.append("<p><strong>Testing different email patterns:</strong></p>")
                    test_patterns = [
                        (r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}', 'Standard pattern'),
                        (r'[^\s]+@[^\s]+', 'Any non-space around @'),
                        (r'.{0,20}@.{0,20}', 'Any 20 chars around @'),
                    ]

                    for pattern, description in test_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            debug_info.append(f"<p>• {description}: <code>{matches[:3]}</code></p>")
                        else:
                            debug_info.append(f"<p>• {description}: No matches</p>")
                else:
                    debug_info.append(f"<p><strong>No @ symbols found in content</strong></p>")

                    # Check if content contains common email-related words
                    email_keywords = ['email', 'e-mail', 'mail', '@', 'contact', 'reach']
                    found_keywords = [word for word in email_keywords if word.lower() in content.lower()]
                    if found_keywords:
                        debug_info.append(f"<p><strong>Email-related keywords found:</strong> {found_keywords}</p>")
                    else:
                        debug_info.append(f"<p><strong>No email-related keywords found</strong></p>")

            debug_info.append("<hr>")

        return "<br>".join(debug_info)

    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/debug/all-cvs')
@login_required
def debug_all_cvs():
    """Debug route to check all CVs in database"""
    try:
        all_jobs = hr_db.get_all_jobs()
        debug_info = []
        debug_info.append("<h2>All CVs in Database</h2>")

        total_cvs = 0
        for job in all_jobs:
            cvs = hr_db.get_cvs_for_job(job.title)
            total_cvs += len(cvs)
            debug_info.append(f"<h3>Job: {job.title}</h3>")
            debug_info.append(f"<p>CVs: {len(cvs)}</p>")

            for cv in cvs:
                debug_info.append(f"<li>{cv.filename} - {getattr(cv, 'candidate_name', 'No name')}</li>")

        debug_info.append(f"<h3>Total CVs in database: {total_cvs}</h3>")

        if total_cvs == 0:
            debug_info.append("<p><strong>❌ No CVs found in database!</strong></p>")
            debug_info.append("<p>Try uploading some CVs first:</p>")
            debug_info.append(f"<a href='/cvs/upload'>Upload CVs</a>")

        return "<br>".join(debug_info)

    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/jobs/delete/<path:job_title>')
@login_required
def delete_job(job_title):
    """Delete a job and all associated CVs"""
    try:
        # Decode URL-encoded job title
        job_title = urllib.parse.unquote(job_title)

        # Get CV count for confirmation message
        cvs = hr_db.get_cvs_for_job(job_title)
        cv_count = len(cvs)

        # Delete job (this will cascade delete CVs and responsible people)
        hr_db.delete_job(job_title)

        if cv_count > 0:
            flash(f'Job "{job_title}" and {cv_count} associated CVs deleted successfully!', 'success')
        else:
            flash(f'Job "{job_title}" deleted successfully!', 'success')
    except Exception as e:
        flash(f'Error deleting job: {str(e)}', 'error')
    return redirect(url_for('jobs'))

@app.route('/cvs')
@login_required
def cvs():
    """List all CVs by job"""
    job_title = request.args.get('job')
    all_jobs = hr_db.get_all_jobs()

    # Convert all_jobs to a list of dictionaries with just the title
    jobs_list = [{'title': job.title} for job in all_jobs]

    if job_title:
        job_cvs = hr_db.get_cvs_for_job(job_title)

        # Convert job_cvs to a list of dictionaries
        cvs_list = []
        for cv in job_cvs:
            cvs_list.append({
                'id': cv.id,
                'filename': cv.filename,
                'candidate_name': getattr(cv, 'candidate_name', None) or 'Unknown'
            })

        return render_template('cvs.html', jobs=jobs_list, selected_job=job_title, cvs=cvs_list)

    return render_template('cvs.html', jobs=jobs_list)

@app.route('/cvs/upload', methods=['GET', 'POST'])
@login_required
def upload_cv():
    """Upload a CV for a job"""
    all_jobs = hr_db.get_all_jobs()

    if not all_jobs:
        flash('Please add a job first before uploading CVs', 'error')
        return redirect(url_for('add_job'))

    if request.method == 'POST':
        job_title = request.form.get('job')
        candidate_name = request.form.get('candidate_name', '').strip()

        if not job_title:
            flash('Please select a job', 'error')
            return render_template('upload_cv.html', jobs=all_jobs)

        if not candidate_name:
            flash('Please enter candidate name', 'error')
            return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title)

        # Check if file was uploaded
        if 'cv_file' not in request.files:
            flash('No file selected', 'error')
            return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

        file = request.files['cv_file']

        if file.filename == '':
            flash('No file selected', 'error')
            return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Extract content based on file type
            content = process_document(filepath)
            if not content:
                flash('Could not extract content from file', 'error')
                return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

            try:
                # Add candidate name to filename for better identification
                display_filename = f"{candidate_name}_{filename}"

                # Add to HR database
                hr_db.add_cv(display_filename, content, job_title, candidate_name)

                flash(f'CV for {candidate_name} uploaded successfully!', 'success')
                return redirect(url_for('cvs', job=job_title))
            except Exception as e:
                flash(f'Error adding CV: {str(e)}', 'error')
                return render_template('upload_cv.html', jobs=all_jobs, selected_job=job_title, candidate_name=candidate_name)

    selected_job = request.args.get('job', '')
    return render_template('upload_cv.html', jobs=all_jobs, selected_job=selected_job)

@app.route('/match')
@login_required
def match():
    """Match CVs to jobs page"""
    all_jobs = hr_db.get_all_jobs()
    # Convert all_jobs to a list of dictionaries with just the title
    jobs_list = [{'title': job.title} for job in all_jobs]
    return render_template('match.html', jobs=jobs_list)

@app.route('/match/results', methods=['POST'])
@login_required
def match_results():
    """Run CV matching for a job"""
    job_title = request.form.get('job')

    if not job_title:
        flash('Please select a job', 'error')
        return redirect(url_for('match'))

    job = hr_db.get_job_by_title(job_title)
    if not job:
        flash(f'Job "{job_title}" not found', 'error')
        return redirect(url_for('match'))

    cvs = hr_db.get_cvs_for_job(job_title)
    if not cvs:
        flash(f'No CVs found for job "{job_title}"', 'error')
        return redirect(url_for('match'))

    # Run matching
    cv_contents = [cv.content for cv in cvs]
    match_results = cv_matcher.match(job.description, cv_contents)

    # Format results for display
    formatted_results = []
    for score, cv_content in match_results:
        # Find the CV object that matches this content
        matching_cv = None
        for cv in cvs:
            if cv.content == cv_content:
                matching_cv = cv
                break

        if matching_cv:
            # Handle case where candidate_name might not exist in older records
            candidate_name = getattr(matching_cv, 'candidate_name', None) or 'Unknown'
            formatted_results.append({
                'filename': matching_cv.filename,
                'candidate_name': candidate_name,
                'score': float(score) * 100  # Convert to percentage
            })

    # Convert job to a dictionary with needed fields
    job_dict = {
        'title': job.title,
        'description': job.description
    }

    return render_template('match_results.html', job=job_dict, results=formatted_results)

@app.route('/extract-excel')
@login_required
def extract_excel():
    """Extract CV data to Excel file for download"""
    job_title = request.args.get('job', '')
    count = request.args.get('count', 'all')
    fields = request.args.get('fields', '').split(',')

    if not job_title or not fields:
        flash('Invalid extraction parameters', 'error')
        return redirect(url_for('jobs'))

    try:
        # Get CVs for the job
        cvs = hr_db.get_cvs_for_job(job_title)

        if not cvs:
            flash(f'No CVs found for job "{job_title}"', 'error')
            return redirect(url_for('jobs'))

        # Limit the number of CVs if specified
        if count != 'all':
            try:
                limit = int(count)
                cvs = cvs[:limit]
            except ValueError:
                pass

        # Initialize CV extractor
        extractor = CVDataExtractor()

        # Create Excel workbook with lazy import
        openpyxl, Font, PatternFill, Alignment = get_openpyxl()
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"CV Data - {job_title}"

        # Create headers
        headers = []
        if 'name' in fields:
            headers.append('Name')
        if 'email' in fields:
            headers.append('Email')
        if 'phone' in fields:
            headers.append('Phone')
        if 'experience' in fields:
            headers.append('Experience')
        if 'skills' in fields:
            headers.append('Skills')
        if 'education' in fields:
            headers.append('Education')
        if 'match_score' in fields:
            headers.append('Match Score')
        headers.append('Filename')

        # Write headers
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")

        # Extract data from each CV
        for row, cv in enumerate(cvs, 2):
            try:
                # Try to find the actual file
                cv_path = None
                possible_paths = []

                # First try the exact filename from database
                exact_path = os.path.join(app.config['UPLOAD_FOLDER'], cv.filename)
                possible_paths.append(exact_path)

                # If filename has candidate name prefix, try without it
                if '_' in cv.filename:
                    parts = cv.filename.split('_', 1)
                    if len(parts) > 1:
                        original_filename = parts[1]
                        original_path = os.path.join(app.config['UPLOAD_FOLDER'], original_filename)
                        possible_paths.append(original_path)

                # Try to find the file
                for path in possible_paths:
                    if os.path.exists(path):
                        cv_path = path
                        break

                # If still not found, try to find any file that ends with the same name
                if not cv_path:
                    upload_dir = app.config['UPLOAD_FOLDER']
                    if os.path.exists(upload_dir):
                        for file in os.listdir(upload_dir):
                            if cv.filename.endswith(file) or file.endswith(cv.filename.split('_')[-1]):
                                cv_path = os.path.join(upload_dir, file)
                                break

                if cv_path and os.path.exists(cv_path):
                    # Extract data using the CV extractor
                    extracted_data = extractor.extract_cv_data(cv_path, fields)

                    col = 1
                    if 'name' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('name', 'N/A'))
                        col += 1
                    if 'email' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('email', 'N/A'))
                        col += 1
                    if 'phone' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('phone', 'N/A'))
                        col += 1
                    if 'experience' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('experience', 'N/A'))
                        col += 1
                    if 'skills' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('skills', 'N/A'))
                        col += 1
                    if 'education' in fields:
                        ws.cell(row=row, column=col, value=extracted_data.get('education', 'N/A'))
                        col += 1
                    if 'match_score' in fields:
                        # Simple match score calculation
                        job = hr_db.get_job_by_title(job_title)
                        if job:
                            score = cv_matcher.calculate_match_score(cv_path, job.description)
                            ws.cell(row=row, column=col, value=f"{score:.2f}%")
                        else:
                            ws.cell(row=row, column=col, value="N/A")
                        col += 1

                    # Add filename
                    ws.cell(row=row, column=col, value=cv.filename)
                else:
                    # File not found, add placeholder data
                    for col in range(1, len(headers)):
                        ws.cell(row=row, column=col, value='File not found')
                    ws.cell(row=row, column=len(headers), value=cv.filename)

            except Exception as e:
                print(f"Error processing CV {cv.filename}: {e}")
                # Add error data
                for col in range(1, len(headers)):
                    ws.cell(row=row, column=col, value='Error processing')
                ws.cell(row=row, column=len(headers), value=cv.filename)

        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

        # Save to BytesIO
        output = BytesIO()
        wb.save(output)
        output.seek(0)

        # Create filename
        safe_job_title = "".join(c for c in job_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"CV_Data_{safe_job_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        flash(f'Error extracting CV data: {str(e)}', 'error')
        return redirect(url_for('jobs'))

# Language switching route
@app.route('/switch_language/<language>')
def switch_language(language):
    """Switch application language"""
    supported_languages = ['en', 'de']
    if language in supported_languages:
        session['language'] = language
        flash(f'Language switched to {language.title()}', 'info')
    else:
        flash('Unsupported language', 'error')

    # Redirect back to the referring page or home
    return redirect(request.referrer or url_for('home'))


# Enhanced context processor for German features
@app.context_processor
def inject_german_features():
    current_language = session.get('language', 'en')

    def get_text_bilingual(key, lang=None):
        lang = lang or current_language
        translations = {
            'home': {'en': 'Home', 'de': 'Startseite'},
            'jobs': {'en': 'Jobs', 'de': 'Stellenanzeigen'},
            'cvs': {'en': 'CVs', 'de': 'Lebensläufe'},
            'logout': {'en': 'Logout', 'de': 'Abmelden'},
            'login': {'en': 'Login', 'de': 'Anmelden'},
            'upload_cv': {'en': 'Upload CV', 'de': 'Lebenslauf hochladen'},
            'add_job': {'en': 'Add Job', 'de': 'Stelle hinzufügen'},
            'language': {'en': 'Language', 'de': 'Sprache'},
            'english': {'en': 'English', 'de': 'Englisch'},
            'german': {'en': 'German', 'de': 'Deutsch'},
            'search': {'en': 'Search', 'de': 'Suchen'},
            'match': {'en': 'Match', 'de': 'Abgleich'},
            'results': {'en': 'Results', 'de': 'Ergebnisse'},
            'candidate': {'en': 'Candidate', 'de': 'Kandidat'},
            'score': {'en': 'Score', 'de': 'Bewertung'},
            'welcome': {'en': 'Welcome to BAUCH HR Management', 'de': 'Willkommen bei BAUCH HR Management'},
            'dashboard': {'en': 'Dashboard', 'de': 'Dashboard'},
            'total_jobs': {'en': 'Total Jobs', 'de': 'Gesamte Stellen'},
            'total_cvs': {'en': 'Total CVs', 'de': 'Gesamte Lebensläufe'},
            'recent_activity': {'en': 'Recent Activity', 'de': 'Letzte Aktivitäten'},
            'bilingual_processing': {'en': 'Bilingual Processing', 'de': 'Zweisprachige Verarbeitung'},
            'german_english_support': {'en': 'German & English Support', 'de': 'Deutsch & Englisch Unterstützung'},
            'email_applicants': {'en': 'Email Applicants', 'de': 'E-Mail an Bewerber'},
            'send_emails': {'en': 'Send Emails', 'de': 'E-Mails senden'}
        }
        return translations.get(key, {}).get(lang, key)

    def extract_email_from_content(content):
        """Extract email address from CV content using multiple robust patterns"""
        import re
        if not content:
            return None

        # Clean the content - remove extra whitespace and normalize
        content = re.sub(r'\s+', ' ', content.strip())

        # Multiple email patterns to catch different formats found in CVs
        email_patterns = [
            # Standard email patterns
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}',

            # Email with spaces (common in PDFs)
            r'[A-Za-z0-9._%+-]+\s*@\s*[A-Za-z0-9.-]+\s*\.\s*[A-Z|a-z]{2,}',

            # Email with line breaks or special characters
            r'[A-Za-z0-9._%+-]+[\s\n]*@[\s\n]*[A-Za-z0-9.-]+[\s\n]*\.[\s\n]*[A-Z|a-z]{2,}',

            # Very permissive pattern for malformed emails
            r'[A-Za-z0-9][A-Za-z0-9._%+-]*@[A-Za-z0-9][A-Za-z0-9.-]*\.[A-Za-z]{2,}',

            # Pattern for emails with dots in unusual places
            r'[A-Za-z0-9]+[._]?[A-Za-z0-9]*@[A-Za-z0-9]+[.-]?[A-Za-z0-9]*\.[A-Za-z]{2,}',
        ]

        for pattern in email_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            if matches:
                # Clean up the found email (remove spaces, newlines)
                email = re.sub(r'\s+', '', matches[0])
                # Validate it looks like a real email
                if '@' in email and '.' in email.split('@')[1]:
                    return email.lower()

        # Last resort: look for any text with @ and try to extract email-like patterns
        at_matches = re.findall(r'[^\s]{1,50}@[^\s]{1,50}', content)
        for match in at_matches:
            # Clean and validate
            cleaned = re.sub(r'[^\w@.-]', '', match)
            if re.match(r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$', cleaned):
                return cleaned.lower()

        return None

    return {
        'now': datetime.now(),
        'get_text': get_text_bilingual,
        'current_language': current_language,
        'bilingual_enabled': True,
        'extract_email_from_content': extract_email_from_content
    }

# This MUST be at the very end of the file for IDE run button to work
if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)

